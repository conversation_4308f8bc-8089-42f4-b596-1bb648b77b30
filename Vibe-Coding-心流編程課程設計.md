# Vibe Coding-心流編程 課程設計

## 課程介紹

在AI時代來臨的今天，程式設計不再是高門檻的技能。本課程結合「心流理論」與現代AI工具，讓零基礎學員透過AI IDE、AI Studio及Gemini API，在9週內從完全不懂程式到能獨立開發Line Bot。課程設計強調「邊做邊學」的沉浸式體驗，讓學員在專注投入中自然習得編程技能。我們相信每個人都有創造數位產品的潛力，只需要適當的引導與工具。透過實作導向的教學方式，學員將體驗到編程的樂趣，並培養解決問題的邏輯思維能力。

## 課程目標

### 學員將學到：
- **AI輔助編程技能**：熟練使用AI IDE進行程式開發
- **API整合能力**：掌握Gemini API的應用與串接
- **Line Bot開發**：從零開始建構完整的聊天機器人
- **邏輯思維訓練**：培養程式設計的結構化思考
- **問題解決能力**：學會拆解複雜問題並逐步實現
- **創意實現技能**：將想法轉化為實際可用的數位產品

### 學期進度規劃：
前3週建立基礎概念，中3週深入實作技能，後3週完成專案並優化

## 課程大綱

### 第1週：AI時代的編程啟蒙
- 認識AI輔助編程的革命性改變
- 體驗AI IDE的神奇功能
- 建立開發環境與工具設定
- 第一個「Hello World」程式體驗

### 第2週：與AI對話學編程
- AI Studio平台深度探索
- 學會與AI溝通的技巧
- 基礎程式語法透過AI學習
- 變數、函數概念實作練習

### 第3週：Gemini API初體驗
- API概念與應用場景介紹
- Gemini API註冊與設定
- 第一次API呼叫成功體驗
- 簡單對話功能實現

### 第4週：Line Bot開發基礎
- Line Developer Console設定
- Webhook概念與實作
- 建立第一個回音機器人
- 訊息接收與回應機制

### 第5週：智能對話機器人
- 整合Gemini API到Line Bot
- 實現智能對話功能
- 處理不同類型訊息
- 錯誤處理與除錯技巧

### 第6週：進階功能開發
- 圖片識別與處理
- 語音訊息功能
- 選單與快速回覆設計
- 使用者體驗優化

### 第7週：個人化專案開發
- 確定個人專案主題
- 功能規劃與設計
- 開始實作個人Line Bot
- 一對一指導與問題解決

### 第8週：專案完善與測試
- 功能完善與bug修復
- 使用者測試與回饋收集
- 效能優化與穩定性提升
- 部署與上線準備

### 第9週：成果發表與未來展望
- 學員作品展示與分享
- 同儕互評與經驗交流
- 課程回顧與學習成果總結
- 後續學習路徑規劃

## 社區回饋構想規劃與效果

### 構想規劃：
1. **數位服務志工隊**：學員組成志工隊，為社區長者提供Line Bot使用教學
2. **社區問題解決專案**：開發解決在地問題的Line Bot（如垃圾車時間查詢、社區活動通知）
3. **跨世代交流平台**：建立連結年輕學員與社區長者的數位橋樑
4. **開源專案貢獻**：將優秀作品開源，供其他社區大學參考使用

### 預期效果：
- **技能傳承**：培養社區數位人才，提升整體數位素養
- **世代共融**：透過科技應用促進不同年齡層的交流合作
- **在地創新**：結合在地需求開發實用的數位工具
- **持續學習**：建立學習型社區，鼓勵終身學習精神
- **社會影響**：示範AI時代的教育創新模式，影響更多教育機構

透過這門課程，我們期望不只是教授技術技能，更要培養學員的創新思維與社會責任感，讓科技真正服務於社區發展。

---

**課程版本**：v1.0.0  
**建立日期**：2024年  
**課程時數**：9週，每週3小時  
**適合對象**：零基礎學員，對AI與程式設計有興趣者
